import { ApolloClient, InMemoryCache, createHttpLink, from } from '@apollo/client';
import { setContext } from '@apollo/client/link/context';
import { onError } from '@apollo/client/link/error';
import { getAuth } from '@react-native-firebase/auth';
import { Platform } from 'react-native';
import Constants from 'expo-constants';
import * as Device from 'expo-device';

// Get GraphQL endpoint based on environment
const getGraphQLEndpoint = () => {
  const prodEndpoint = process.env.EXPO_PUBLIC_GRAPHQL_ENDPOINT;
  let devEndpoint = process.env.EXPO_PUBLIC_DEV_GRAPHQL_ENDPOINT;

  if (__DEV__) {
    const port = devEndpoint?.split(':').pop() || '3000';

    if (Platform.OS === 'android') {
      // Use the special IP for Android emulators
      devEndpoint = `http://********:${port}`;
    } else if (Platform.OS === 'ios' && Device.isDevice) {
      // For physical iOS devices, use the host machine's IP from expo-constants
      const hostUri = Constants.expoConfig?.hostUri;
      if (hostUri) {
        const host = hostUri.split(':')[0];
        devEndpoint = `http://${host}:${port}`;
      }
    }
    // For iOS simulators, the default devEndpoint (e.g., http://localhost:3000) should work.
  }

  const selectedEndpoint = process.env.EXPO_PUBLIC_BACKEND_ENV;
  console.log('apolloClient.ts: selectedEndpoint', selectedEndpoint);
  console.log('apolloClient.ts: prodEndpoint', prodEndpoint);
  console.log('apolloClient.ts: devEndpoint', devEndpoint);
  if (selectedEndpoint === 'production') {
    console.log('apolloClient.ts: Using production GraphQL endpoint:', prodEndpoint);
    return prodEndpoint;
  } else if (selectedEndpoint === 'development') {
    console.log('apolloClient.ts: Using development GraphQL endpoint:', devEndpoint);
    return devEndpoint;
  }
  console.log('apolloClient.ts: no other option, Using development GraphQL endpoint:', devEndpoint);
  return devEndpoint;
};

// Your GraphQL endpoint
const httpLink = createHttpLink({
  uri: getGraphQLEndpoint(),
  credentials: 'include', // Include credentials for CORS
});

// Auth link to add Firebase ID token to requests
const authLink = setContext(async (_, { headers }) => {
  try {
    const authInstance = getAuth();
    const user = authInstance.currentUser;
    let token = null;
     
    if (user) {
      // Get the Firebase ID token
      token = await user.getIdToken();
    }

    return {
      headers: {
        ...headers,
        authorization: token ? `Bearer ${token}` : '',
        // Add Apollo-specific headers for production
        'Apollo-Require-Preflight': 'true',
      },
    };
  } catch (error) {
    console.error('Error getting Firebase ID token:', error);
    return {
      headers: {
        ...headers,
        authorization: '',
        'Apollo-Require-Preflight': 'true',
      },
    };
  }
});

// Error link to handle authentication errors
const errorLink = onError(({ graphQLErrors, networkError, operation, forward }) => {
  if (graphQLErrors) {
    graphQLErrors.forEach(({ message, locations, path, extensions }) => {
      // Only log detailed errors in development
      if (__DEV__) {
        console.error(
          `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
        );
      } else {
        console.error(`[GraphQL error]: ${message}`);
      }
      
      // Handle authentication errors
      if (extensions?.code === 'UNAUTHENTICATED') {
        console.log('User is not authenticated, consider redirecting to login');
      }
    });
  }

  if (networkError) {
    // Log network errors appropriately for environment
    if (__DEV__) {
      console.error(`[Network error]: ${networkError}`);
    } else {
      console.error(`[Network error]: Connection failed`);
    }
    
    // Handle 401 errors
    if ('statusCode' in networkError && networkError.statusCode === 401) {
      console.log('Received 401, token might be expired');
    }
    
    // Handle network connectivity issues in production
    if ('networkError' in networkError || networkError.message.includes('Network request failed')) {
      console.log('Network connectivity issue detected');
    }
  }
});

// Create Apollo Client
export const apolloClient = new ApolloClient({
  link: from([errorLink, authLink, httpLink]),
  cache: new InMemoryCache({
    typePolicies: {
      Query: {
        fields: {
          me: {
            merge(existing, incoming, { mergeObjects }) {
              return mergeObjects(existing, incoming);
            },
          },
          threads: {
            keyArgs: ['communityId'], // Cache separately by community
            merge(existing = [], incoming, { args }) {
              // Handle pagination properly
              if (args?.offset === 0 || !existing.length) {
                return incoming; // Fresh fetch or first page
              }
              
              // For pagination, merge arrays
              const existingIds = new Set(existing.map((thread: any) => thread._id));
              const newThreads = incoming.filter((thread: any) => !existingIds.has(thread._id));
              return [...existing, ...newThreads];
            },
          },
          comments: {
            keyArgs: ['threadId'], // Cache separately by thread
            merge(existing = [], incoming, { args }) {
              if (args?.offset === 0 || !existing.length) {
                return incoming;
              }
              const existingIds = new Set(existing.map((comment: any) => comment._id));
              const newComments = incoming.filter((comment: any) => !existingIds.has(comment._id));
              return [...existing, ...newComments];
            },
          },
        },
      },
      Thread: {
        keyFields: ['_id'], // Ensure proper normalization by _id
        fields: {
          reactionCounts: {
            merge(existing, incoming) {
              return { ...existing, ...incoming };
            },
          },
        },
      },
      Comment: {
        keyFields: ['_id'], // Ensure proper normalization by _id
        fields: {
          reactionCounts: {
            merge(existing, incoming) {
              return { ...existing, ...incoming };
            },
          },
        },
      },
    },
  }),
  defaultOptions: {
    watchQuery: {
      errorPolicy: 'all',
      fetchPolicy: 'cache-first', // Always prefer cache for better UX
      notifyOnNetworkStatusChange: true,
    },
    query: {
      errorPolicy: 'all',
      fetchPolicy: 'cache-first',
    },
    mutate: {
      errorPolicy: 'all',
    },
  },
  connectToDevTools: __DEV__,
});

// Function to reset Apollo cache when user signs out
export const resetApolloCache = async () => {
  try {
    // First, clear the store to prevent refetching of active queries
    await apolloClient.clearStore();
    // Then, reset the store to ensure a clean slate for the next user
    await apolloClient.resetStore();
  } catch (error) {
    console.error('Error resetting Apollo cache:', error);
  }
}; 