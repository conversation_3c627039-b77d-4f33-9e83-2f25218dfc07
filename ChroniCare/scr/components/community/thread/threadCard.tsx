import { View, Text, StyleSheet, Image, Pressable, TouchableOpacity, Platform } from 'react-native'
import React, { useMemo, useState, useRef } from 'react'
import { useTheme } from '@/scr/context/themeContext'
import Reactions from '../reactions/reactions';
import CommentCounter from '../comments/comment';
import TimeStamp from '../TimeStamp';
import { useRouter } from 'expo-router';
import { useFragment } from '@apollo/client';
import { THREAD_FRAGMENT } from '@/scr/graphql/fragments';
import { Ellipsis } from 'lucide-react-native';
import ThreadOptionsMenu from './optionsThread';
import { useUser } from '@/scr/context/userContext';

interface ThreadCardProps {
    threadId: string;
    isClickable?: boolean;
}

const ThreadCard = ({ threadId, isClickable = true }: ThreadCardProps) => {
    const { theme } = useTheme();
    const { data: thread } = useFragment({
        from: {
            __typename: 'Thread',
            _id: threadId,
        },
        fragment: THREAD_FRAGMENT,
    });
    const router = useRouter();
    const { user } = useUser();
    const [menuVisible, setMenuVisible] = useState(false);
    const [menuPosition, setMenuPosition] = useState({ x: 0, y: 0 });
    const ellipsisRef = useRef<any>(null);

    const handlePress = () => {
        if (isClickable) {
            router.push(`/community/${threadId}`);
        }
    };

    const handleOptionsPress = () => {
        ellipsisRef.current?.measure((_fx: number, _fy: number, _width: number, height: number, px: number, py: number) => {
            const yPosition = Platform.OS === 'android' ? py : py + height;
            setMenuPosition({ x: px - 130, y: yPosition });
        });
        setMenuVisible(true);
    };

    const styles = useMemo(() => StyleSheet.create({
        postContainer: {
            gap: theme.spacing.spacing.s2,
        },
        authorContainer: {
            flexDirection: 'row',
        },
        authorImage: {
            width: 32,
            height: 32,
            borderRadius: 16,
            marginRight: theme.spacing.spacing.s2,
        },
        authorInfo: {
            flex: 1,
        },
        authorMetaContainer: {
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'flex-start',
        },
        authorName: {
            ...theme.textVariants.text('md', 'regular'),
            color: theme.colors.Text.text900,
            marginRight: theme.spacing.spacing.s2,
        },
        authorDetails: {
            ...theme.textVariants.text('xs', 'regular'),
            color: theme.colors.Text.text700,
        },
        authorDetailsDot: {
            marginHorizontal: theme.spacing.spacing.s1,
        },
        postTitle: {
            ...theme.textVariants.heading('sm', 'bold'),
            color: theme.colors.Text.text900,
        },
        postContent: {
            ...theme.textVariants.text('sm', 'regular'),
            color: theme.colors.Text.text900,
        },
        actionBar: {
            flexDirection: 'row',
            alignItems: 'center',
            gap: 8,
        },
    }), [theme]);

    if (!thread) {
        return null;
    }

    const isAuthor = user?.id === thread.author.authorId;

    return (
        <Pressable onPress={handlePress} disabled={!isClickable}>
            <View style={styles.postContainer}>
                <View style={styles.authorContainer}>
                    <Image source={{ uri: thread.author.photoURL }} style={styles.authorImage} />
                    <View style={styles.authorInfo}>
                        <View style={styles.authorMetaContainer}>
                            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                                <Text style={styles.authorName}>{thread.author.displayName}</Text>
                                <TimeStamp createdAt={thread.createdAt} />
                            </View>
                            <TouchableOpacity ref={ellipsisRef} onPress={handleOptionsPress}>
                                <Ellipsis size={20} color={theme.colors.Background.background700} />
                            </TouchableOpacity>
                        </View>
                        <View style={{ flexDirection: 'row' }}>
                            <Text style={styles.authorDetails}>{thread.author.userType}</Text>
                            {thread.author.condition && (
                                <>
                                    <Text style={[styles.authorDetails, styles.authorDetailsDot]}>•</Text>
                                    <Text style={styles.authorDetails}>{thread.author.condition}</Text>
                                </>
                            )}
                        </View>
                    </View>
                </View>
                <Text style={styles.postTitle}>{thread.title}</Text>
                <Text style={styles.postContent}>{thread.content}</Text>
                
                <View style={styles.actionBar}>
                    <View onStartShouldSetResponder={() => true}>
                        <Reactions type="thread" thread={thread} />
                    </View>
                    <CommentCounter count={thread.commentCount} />
                </View>
            </View>
            <ThreadOptionsMenu
                isVisible={menuVisible}
                onClose={() => setMenuVisible(false)}
                isAuthor={isAuthor}
                position={menuPosition}
            />
        </Pressable>
    );
}

export default React.memo(ThreadCard)