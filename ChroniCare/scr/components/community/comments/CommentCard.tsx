import { View, Text, StyleSheet, Image, TouchableOpacity } from 'react-native';
import React, { useMemo } from 'react';
import { useTheme } from '@/scr/context/themeContext';
import TimeStamp from '../TimeStamp';
import Reactions from '../reactions/reactions';
import { Author, ReactionCount } from '@/scr/graphql/fragments';
import { Ellipsis, Reply } from 'lucide-react-native';

export interface Comment {
    _id: string;
    threadId: string;
    author: Author;
    content: string;
    createdAt: string;
    parentCommentId?: string;
    replyCount: number;
    reactionCounts: ReactionCount;
    myReaction: string | null;
}

interface CommentCardProps {
    comment: Comment;
    nestingLevel?: number;
    onReply: (comment: Comment) => void;
}

const CommentCard = ({ comment, nestingLevel = 0, onReply }: CommentCardProps) => {
    const { theme } = useTheme();

    const handleOptionsPress = () => {
        // Handle options menu press
    };

    const handleReplyPress = () => {
        onReply(comment);
    };

    const styles = useMemo(() => StyleSheet.create({
        commentRow: {
            flexDirection: 'row',
        },
        indentationContainer: {
            width: 12,
            alignItems: 'center',
            marginRight: theme.spacing.spacing.s1, 
        },
        verticalLine: {
            width: 1,
            flex: 1,
            backgroundColor: theme.colors.Background.background200,
        },
        contentContainer: {
            flex: 1,
            gap: theme.spacing.spacing.s2,
            paddingBottom: theme.spacing.spacing.s2,
        },
        authorContainer: {
            flexDirection: 'row',
        },
        authorImage: {
            width: 32,
            height: 32,
            borderRadius: 16,
            marginRight: theme.spacing.spacing.s2,
        },
        authorInfo: {
            flex: 1,
        },
        authorMetaContainer: {
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'flex-start',
        },
        authorName: {
            ...theme.textVariants.text('md', 'regular'),
            color: theme.colors.Text.text900,
            marginRight: theme.spacing.spacing.s2,
        },
        authorDetails: {
            ...theme.textVariants.text('xs', 'regular'),
            color: theme.colors.Text.text700,
        },
        authorDetailsDot: {
            marginHorizontal: theme.spacing.spacing.s1,
        },
        commentContent: {
            ...theme.textVariants.text('sm', 'regular'),
            color: theme.colors.Text.text900,
        },
        actionBar: {
            flexDirection: 'row',
            alignItems: 'center',
            gap: 8,
        },
    }), [theme]);

    if (!comment) {
        return null;
    }

    return (
        <View style={styles.commentRow}>
            <View style={styles.indentationContainer}>
                <View style={styles.verticalLine} />
            </View>
            {Array.from({ length: nestingLevel }).map((_, index) => (
                <View key={`indent-${index}`} style={styles.indentationContainer}>
                    <View style={styles.verticalLine} />
                </View>
            ))}
            <View style={styles.contentContainer}>
                <View style={styles.authorContainer}>
                    <Image
                        source={
                            comment.author.photoURL
                                ? { uri: comment.author.photoURL }
                                : require('../../../../assets/images/placeholderProfileImage.png')
                        }
                        style={styles.authorImage}
                    />
                    <View style={styles.authorInfo}>
                        <View style={styles.authorMetaContainer}>
                            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                                <Text style={styles.authorName}>{comment.author.displayName}</Text>
                                <TimeStamp createdAt={comment.createdAt} />
                            </View>
                            <TouchableOpacity onPress={handleOptionsPress}>
                                <Ellipsis size={20} color={theme.colors.Background.background700} />
                            </TouchableOpacity>
                        </View>
                        <View style={{ flexDirection: 'row' }}>
                            <Text style={styles.authorDetails}>{comment.author.userType}</Text>
                            {comment.author.condition && (
                                <>
                                    <Text style={[styles.authorDetails, styles.authorDetailsDot]}>•</Text>
                                    <Text style={styles.authorDetails}>{comment.author.condition}</Text>
                                </>
                            )}
                        </View>
                    </View>
                </View>

                <Text style={styles.commentContent}>{comment.content}</Text>

                <View style={styles.actionBar}>
                    <Reactions type="comment" comment={comment} showBorder={false} />
                    <TouchableOpacity onPress={handleReplyPress}>
                        <Reply size={20} color={theme.colors.Text.text600} />
                    </TouchableOpacity>
                </View>
            </View>
        </View>
    );
};

export default CommentCard;