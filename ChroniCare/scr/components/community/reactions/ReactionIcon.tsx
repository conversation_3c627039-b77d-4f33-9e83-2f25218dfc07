import React from 'react';
import { View, StyleSheet } from 'react-native';
import { useTheme } from '@/scr/context/themeContext';
import { ReactionCount } from '@/scr/graphql/fragments';
import { Heart, HeartHandshake, Laugh, Lightbulb, IceCream } from 'lucide-react-native';

type ReactionType = keyof Omit<ReactionCount, '__typename'>;

const iconMap: Record<ReactionType, React.ElementType> = {
  love: Heart,
  withYou: HeartHandshake,
  funny: Laugh,
  insightful: Lightbulb,
  poop: IceCream,
};

interface ReactionIconProps {
  reactionType: ReactionType;
  isSelected: boolean;
  size?: number;
  strokeWidth?: number;
}

const ReactionIcon = ({
  reactionType,
  isSelected,
  size = 16,
  strokeWidth = 2.5,
}: ReactionIconProps) => {
  const { theme } = useTheme();
  const IconComponent = iconMap[reactionType];

  const iconColors = theme.colors.Reaction;

  const strokeColor = isSelected ? iconColors.stroke[reactionType] : theme.colors.Text.text900;
  const fillColor = isSelected ? iconColors.fill[reactionType] : 'transparent';
  const backgroundColor = isSelected ? iconColors.background[reactionType] : theme.colors.Background.background100;
  
  const styles = StyleSheet.create({
    iconWrapper: {
      width: size + 8,
      height: size + 8,
      borderRadius: (size + 8) / 2,
      backgroundColor: backgroundColor,
      justifyContent: 'center',
      alignItems: 'center',
    },
  });

  return (
    <View style={styles.iconWrapper}>
      <IconComponent
        size={size}
        stroke={strokeColor}
        fill={fillColor}
        strokeWidth={strokeWidth}
      />
    </View>
  );
};

export default React.memo(ReactionIcon); 