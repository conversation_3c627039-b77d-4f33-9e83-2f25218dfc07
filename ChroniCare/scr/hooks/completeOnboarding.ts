// src/screens/OnboardingReviewScreen.tsx (or wherever you trigger "Finish")
import React from 'react';
import { useMutation } from '@apollo/client';
import { COMPLETE_ONBOARDING } from '../graphql/queries';
import { useOnboardingStore } from '../utils/onboardingStore';
import { useUser } from '../context/userContext';

export const useCompleteOnboarding = () => {
  const getOnboardingData = useOnboardingStore((state) => state.getOnboardingData);
  const { refetchUser } = useUser();

  const [runMutation, { data, loading, error }] = useMutation(COMPLETE_ONBOARDING);

  const handleCompleteOnboarding = async (): Promise<boolean> => {
    const onboardingData = getOnboardingData();

    // Pass the icdCode instead of the label
    const cleanSelectedDiseases = onboardingData.diseases.selectedDiseases.map(({ id, icdCode }) => ({
        id,
        icdCode,
    }));

    const variables = {
      data: {
        consent: onboardingData.consent,
        personalInfo: onboardingData.personalInfo,
        profilePicture: {
          imageUri: onboardingData.profilePicture.imageUri,
        },
        diseases: {
          selectedDiseases: cleanSelectedDiseases,
        },
        userTypes: {
          diseaseUserTypes: JSON.stringify(onboardingData.userTypes.diseaseUserTypes),
        },
        medications: {
          diseaseRelatedMedications: JSON.stringify(onboardingData.medications.diseaseRelatedMedications),
          unrelatedMedications: onboardingData.medications.unrelatedMedications.map(med => ({
            ...med,
            medication: {
                id: med.medication.id,
                label: med.medication.label
            }
          })),
        },
      },
    };

    try {
      const result = await runMutation({ variables });
      // The backend now returns a boolean directly on the completeOnboarding field
      const success = result.data?.completeOnboarding === true;
      if (success) {
        console.log('Onboarding completion successful, refetching user data...');
        await refetchUser();
      }
      return success;
    } catch (e) {
      console.error('Onboarding completion error', e);
      return false;
    }
  };

  return {
    completeOnboarding: handleCompleteOnboarding,
    data,
    loading,
    error,
  };
};


