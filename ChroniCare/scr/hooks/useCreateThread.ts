import { useMutation } from '@apollo/client';
import { useRouter } from 'expo-router';
import { useUser } from '../context/userContext';
import { CREATE_THREAD, GET_THREADS, GET_COMMENTS } from '../graphql/queries';
import { Alert } from 'react-native';

export const useCreateThread = () => {
  const { user } = useUser();
  const router = useRouter();

  const [createThreadMutation, { loading, error }] = useMutation(CREATE_THREAD, {
    optimisticResponse: (vars: { input: { title: string; content: string; communityId: string; author: { displayName: any; }; imageUrls?: string[]; }; }) => {
      const { input } = vars;
      const displayName = input.author.displayName;

      return {
        __typename: 'Mutation',
        createThread: {
          __typename: 'Thread',
          _id: '123',
          title: input.title,
          content: input.content,
          imageUrls: input.imageUrls || [],
          communityId: input.communityId,
          author: {
            __typename: 'ThreadAuthor',
            authorId: user!.id,
            displayName: displayName,
            photoURL: user!.photoURL,
            condition: user!.condition,
            userType: user!.userType,
          },
          createdAt: new Date().toISOString(),
          commentCount: 0,
          myReaction: null,
          reactionCounts: {
            __typename: 'ReactionCount',
            love: 0,
            withYou: 0,
            funny: 0,
            insightful: 0,
            poop: 0,
          },
        },
      };
    },
    onCompleted: (data) => {
      if (data.createThread) {
        const communityId = data.createThread.communityId;
        // Replace navigates to the community tab and passes the communityId
        // so the correct community's threads are displayed.
        router.replace({
          pathname: '/(auth)/(tabs)/community',
          params: { communityId },
        });
      }
    },
    onError: (error) => {
      Alert.alert('Error Creating Thread', error.message);
    },
    refetchQueries: [
      GET_COMMENTS
    ]
  });

  const createThread = (title: string, content: string, communityId: string, imageUrls?: string[]) => {
    if (!user) {
      Alert.alert('Authentication Error', 'You must be logged in to create a thread.');
      return;
    }

    const author = {
      authorId: user.id,
      displayName: user.displayName || `${user.firstName || ''} ${user.lastName || ''}`.trim() || 'Anonymous User',
      condition: user.condition || undefined,
      userType: user.userType || 'PATIENT',
      photoURL: user.photoURL || undefined,
    };

    createThreadMutation({
      variables: {
        input: {
          title,
          content,
          communityId,
          author,
          imageUrls: imageUrls || [],
        },
      },
      refetchQueries: [
        {
          query: GET_THREADS,
          variables: {
            communityId,
            limit: 10,
          },
        },
      ],
    });
  };

  return { createThread, loading, error };
}; 