import { useState } from 'react';
import { useMutation } from '@apollo/client';
import { CREATE_THREAD_IMAGE_UPLOAD_URL } from '../graphql/queries';
import { Alert } from 'react-native';
import * as ImagePicker from 'expo-image-picker';

export interface UseThreadImagesResult {
  uploadedImages: string[];
  isUploading: boolean;
  error: any;
  uploadImage: (imageUri: string, mimeType: string) => Promise<string | null>;
  selectAndUploadImage: () => Promise<void>;
  removeImage: (imageUrl: string) => void;
  clearImages: () => void;
}

export const useThreadImages = (): UseThreadImagesResult => {
  const [uploadedImages, setUploadedImages] = useState<string[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState<any>(null);

  const [createUploadUrl] = useMutation(CREATE_THREAD_IMAGE_UPLOAD_URL, {
    onError: (error) => {
      console.error('Error creating upload URL:', error);
      setError(error);
    }
  });

  const uploadImage = async (imageUri: string, mimeType: string): Promise<string | null> => {
    try {
      setIsUploading(true);
      setError(null);

      // Step 1: Get signed upload URL from backend
      const { data: uploadData } = await createUploadUrl({
        variables: { contentType: mimeType },
      });
      const { signedUrl, publicUrl } = uploadData.createThreadImageUploadUrl;

      // Step 2: Upload image directly to Google Cloud Storage
      const response = await fetch(imageUri);
      const blob = await response.blob();
      
      console.log('Uploading thread image to Google Cloud Storage');
      const uploadResponse = await fetch(signedUrl, {
        method: 'PUT',
        headers: {
          'Content-Type': mimeType,
        },
        body: blob,
      });

      if (!uploadResponse.ok) {
        const responseText = await uploadResponse.text();
        console.error('Upload failed with status:', uploadResponse.status);
        console.error('Upload response body:', responseText);
        throw new Error(`Failed to upload image to storage: ${uploadResponse.status} - ${responseText}`);
      }

      // Step 3: Add to uploaded images list
      setUploadedImages(prev => [...prev, publicUrl]);
      console.log('Thread image uploaded successfully:', publicUrl);
      
      return publicUrl;
    } catch (error) {
      console.error('Error uploading thread image:', error);
      setError(error);
      Alert.alert('Upload Error', 'Failed to upload image. Please try again.');
      return null;
    } finally {
      setIsUploading(false);
    }
  };

  const selectAndUploadImage = async (): Promise<void> => {
    try {
      // Request permissions
      const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();
      
      if (permissionResult.granted === false) {
        Alert.alert('Permission Required', 'Permission to access media library is required!');
        return;
      }

      // Pick image
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ['images'],
        allowsEditing: true,
        aspect: [16, 9],
        quality: 0.8,
        base64: false,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        const asset = result.assets[0];
        const mimeType = asset.mimeType || 'image/jpeg';
        await uploadImage(asset.uri, mimeType);
      }
    } catch (error) {
      console.error('Error selecting image:', error);
      Alert.alert('Error', 'Failed to select image. Please try again.');
    }
  };

  const removeImage = (imageUrl: string) => {
    setUploadedImages(prev => prev.filter(url => url !== imageUrl));
  };

  const clearImages = () => {
    setUploadedImages([]);
    setError(null);
  };

  return {
    uploadedImages,
    isUploading,
    error,
    uploadImage,
    selectAndUploadImage,
    removeImage,
    clearImages,
  };
}; 