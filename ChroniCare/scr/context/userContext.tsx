import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { ApolloError, useQuery } from '@apollo/client';
import { GET_ME } from '../graphql/queries';
import { useAuth } from './authContext';

export interface User {
  id: string;
  firebaseUid: string;
  email: string | null;
  displayName: string | null;
  firstName: string | null;
  lastName: string | null;
  photoURL: string | null;
  onboardingCompleted: boolean;
  condition: string | null;
  userType: string | null;
  communities: {
    id: string;
    name: string;
  }[] | null;
}

interface UserContextType {
  user: User | null;
  loading: boolean;
  error?: ApolloError;
  networkError: boolean;
  refetchUser: () => Promise<any>;
  setSignupFlowActive: (isActive: boolean) => void;
}

export const UserContext = createContext<UserContextType | undefined>(undefined);

function isNetworkError(error: ApolloError): boolean {
  return error.networkError !== null || 
         error.message?.includes('Network error') ||
         error.message?.includes('Failed to fetch') ||
         error.message?.includes('network') ||
         error.graphQLErrors?.some(err => err.extensions?.code === 'NETWORK_ERROR') ||
         false;
}

export function UserProvider({ children }: { children: ReactNode }) {
  const { user: authUser, initializing: authInitializing } = useAuth();
  const [isSignupFlowActive, setSignupFlowActive] = useState(false);
  const { data, loading, refetch, error } = useQuery(GET_ME, {
    skip: !authUser || isSignupFlowActive,
    errorPolicy: 'all',
    fetchPolicy: 'cache-and-network',
  });

  const [user, setUser] = useState<User | null>(null);
  const [networkError, setNetworkError] = useState(false);

  useEffect(() => {
    if (data && data.me) {
      setUser(data.me);
      setNetworkError(false);
    } else if (!authInitializing && !authUser) {
      setUser(null);
      setNetworkError(false);
    } else if (error && authUser) {
      if (isNetworkError(error)) {
        setNetworkError(true);
      } else {
        setNetworkError(false);
        setUser(null);
      }
    }
  }, [data, authUser, authInitializing, error]);

  const refetchUser = async () => {
    try {
      const result = await refetch();
      setNetworkError(false);
      return result;
    } catch (err) {
      if (err instanceof Error && isNetworkError(err as ApolloError)) {
        setNetworkError(true);
      }
      throw err;
    }
  };

  return (
    <UserContext.Provider value={{ 
      user, 
      loading: authInitializing || loading, 
      refetchUser, 
      setSignupFlowActive, 
      error,
      networkError 
    }}>
      {children}
    </UserContext.Provider>
  );
}

export function useUser() {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
} 