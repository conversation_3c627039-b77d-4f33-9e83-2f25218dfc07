import { Stack, useRouter, useSegments } from 'expo-router';
import { useEffect, useState } from 'react';
import { View, ActivityIndicator } from 'react-native';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { OnboardingProvider, useOnboarding } from '../scr/context/onboardingContext';
import { ThemeProvider } from '../scr/context/themeContext';
import { AuthProvider, useAuth } from '../scr/context/authContext';
import { ApolloProvider } from '../scr/providers/ApolloProvider';
import { KeyboardProvider } from 'react-native-keyboard-controller';
import { UserProvider, useUser } from '../scr/context/userContext';
import { NetworkErrorScreen } from '../scr/components/NetworkErrorScreen';


function RootLayoutContent() {
	const router = useRouter();
	const segments = useSegments();
	const { isLoading: onboardingLoading, hasCompletedOnboarding, networkError } = useOnboarding();
	const { user, initializing } = useAuth();
	const { refetchUser } = useUser();
	const [isRetrying, setIsRetrying] = useState(false);

	useEffect(() => {
		if (initializing || onboardingLoading) return;

		const inAuthGroup = segments[0] === '(auth)';
		const inOnboardingFlow = segments.some(segment => segment === '(onboarding)');

		if (user) {
			if (hasCompletedOnboarding) {
				if (!inAuthGroup || inOnboardingFlow) {
					router.replace('/(auth)/(tabs)/community');
				}
			} else {
				const inOnboardingFlow = segments.some(segment => segment === '(onboarding)');
				if (!inOnboardingFlow) {
					router.replace('/(auth)/(onboarding)/consent');
				}
			}
		} else {
			const inPublicGroup = segments[0] === '(public)';
			if (!inPublicGroup) {
				if (hasCompletedOnboarding) {
					router.replace('/(public)/login');
				} else {
					router.replace('/(public)/intro');
				}
			}
		}
	}, [user, initializing, hasCompletedOnboarding, onboardingLoading, segments, router]);

	const handleRetry = async () => {
		setIsRetrying(true);
		try {
			await refetchUser();
		} catch (error) {
			console.error('Retry failed:', error);
		} finally {
			setIsRetrying(false);
		}
	};

	if (initializing || onboardingLoading) {
		return (
			<View
				style={{
					alignItems: 'center',
					justifyContent: 'center',
					flex: 1
				}}
			>
				<ActivityIndicator size="large" />
			</View>
		);
	}

	if (user && networkError) {
		return (
			<NetworkErrorScreen
				onRetry={handleRetry}
				isRetrying={isRetrying}
			/>
		);
	}

	return (
		<Stack>
			<Stack.Screen name="index" options={{ headerShown: false }} />
			<Stack.Screen name="(public)" options={{ headerShown: false }} />
			<Stack.Screen name="(auth)" options={{ headerShown: false }} />
		</Stack>
	);
}

export default function RootLayout() {
	return (
		<GestureHandlerRootView style={{ flex: 1 }}>
			<KeyboardProvider>
				<ThemeProvider>
					<AuthProvider>
						<ApolloProvider>
							<UserProvider>
								<OnboardingProvider>
									<RootLayoutContent />
								</OnboardingProvider>
							</UserProvider>
						</ApolloProvider>
					</AuthProvider>
				</ThemeProvider>
			</KeyboardProvider>
		</GestureHandlerRootView>
	);
}