import React from "react";
import { View, Text, StyleSheet, ActivityIndicator, RefreshControl, ScrollView } from "react-native";
import { useMemo, useCallback } from "react";
import { useTheme } from "@/scr/context/themeContext";
import Label from "@/scr/components/forms/Label";
import { useQuery } from "@apollo/client";
import { GET_THREADS } from "@/scr/graphql/queries";
import ThreadCard from "@/scr/components/community/thread/threadCard";
import { useUser } from "@/scr/context/userContext";
import { FlashList } from "@shopify/flash-list";
import { useLocalSearchParams } from "expo-router";
import { Thread } from "@/scr/graphql/fragments";

const CommunityScreen = () => {
  const { theme } = useTheme();
  const { user } = useUser();
  const params = useLocalSearchParams<{ communityId?: string }>();
  
  const communityId = useMemo(() => {
    return params.communityId || user?.communities?.[0]?.id || '';
  }, [params.communityId, user?.communities]);

  const { data, loading, error, refetch } = useQuery<{ threads: Thread[] }>(GET_THREADS, {
    variables: {
      limit: 10,
      communityId: communityId,
    },
    skip: !communityId,
    notifyOnNetworkStatusChange: true,
  });

  const threadList: Thread[] = useMemo(() => data?.threads || [], [data?.threads]);
  const communityName = useMemo(() => {
    return user?.communities?.find(c => c.id === communityId)?.name || '';
  }, [communityId, user?.communities]);

  const handleRefresh = useCallback(async () => {
    try {
      await refetch();
    } catch (error) {
      console.error('Error refreshing threads:', error);
    }
  }, [refetch]);

  const styles = useMemo(() => StyleSheet.create({
    container: {
      flex: 1,
      paddingHorizontal: theme.spacing.spacing.s4,
      gap: theme.spacing.spacing.s4,
      backgroundColor: theme.colors.Background.background0,
    },
    threadContainer: {
      gap: theme.spacing.spacing.s2,
    },
    labelContainer: {
      flexDirection: 'row',
      gap: theme.spacing.spacing.s2,
    },
    centered: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    dividerContainer: {
      paddingBottom: theme.spacing.spacing.s4,
    },
    divider: {
      height: 1,
      backgroundColor: theme.colors.Background.background100,
    }
  }), [theme]); 

  const renderContent = () => {
    if (loading) {
      return (
        <View style={styles.centered}>
          <ActivityIndicator size="large" />
        </View>
      );
    }

    if (error) {
      return (
        <View style={styles.centered}>
          <Text style={{ color: theme.colors.Text.text0 }}>Error: {error.message}</Text>
        </View>
      );
    }
    
    if (!loading && threadList.length === 0) {
      return (
        <ScrollView
          contentContainerStyle={styles.centered}
          refreshControl={
            <RefreshControl
              refreshing={loading}
              onRefresh={handleRefresh}
              tintColor={theme.colors.Background.background900}
              colors={[theme.colors.Background.background900]}
            />
          }
        >
          <Text style={{ color: theme.colors.Text.text900 }}>No posts found in this community.</Text>
        </ScrollView>
      )
    }

    return (
      <FlashList<Thread>
        data={threadList}
        keyExtractor={(item) => item._id}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={loading}
            onRefresh={handleRefresh}
            tintColor={theme.colors.Background.background900}
            colors={[theme.colors.Background.background900]}
          />
        }
        renderItem={({ item }) => (
          <View style={styles.threadContainer}>
            <ThreadCard threadId={item._id} />
            <View style={styles.dividerContainer}>
              <View style={styles.divider} />
            </View>
          </View>
        )}
        estimatedItemSize={180}
      />
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.labelContainer}>
        <Label text={communityName} variant="solid" colorScheme="primary" />
      </View>
      {renderContent()}
    </View>
  );
};

export default CommunityScreen;