import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  TextInput,
  Alert,
  SafeAreaView,
  ScrollView,
  Image as RNImage,
} from "react-native";
import React from "react";
import { useTheme } from "../../../scr/context/themeContext";
import { XCircle, Plus, Camera, Image, X } from "lucide-react-native";
import { useRouter } from "expo-router";
import Button from "../../../scr/components/button";
import { useUser } from "../../../scr/context/userContext";
import { ActivityIndicator } from "react-native";
import { useCreateThread } from "../../../scr/hooks/useCreateThread";
import { useThreadImages } from "../../../scr/hooks/useThreadImages";
import { KeyboardAvoidingView } from "react-native-keyboard-controller";
import CommunitySelector from "../../../scr/components/community/CommunitySelector";
import * as ImagePicker from 'expo-image-picker';

const CreatePostScreen = () => {
  const { theme } = useTheme();
  const router = useRouter();
  const [selectedCommunity, setSelectedCommunity] = React.useState<string | null>(
    null
  );
  const [title, setTitle] = React.useState("");
  const [content, setContent] = React.useState("");
  const titleInputRef = React.useRef<TextInput>(null);

  const { user, loading: userLoading, error: userError } = useUser();
  const { createThread, loading: createLoading } = useCreateThread();
  const { 
    uploadedImages, 
    isUploading, 
    uploadImage,
    selectAndUploadImage, 
    removeImage, 
    clearImages 
  } = useThreadImages();

  React.useEffect(() => {
    if (user?.communities && user.communities.length === 1) {
      setSelectedCommunity(user.communities[0].id);
    }
  }, [user]);

  React.useEffect(() => {
    // Focus the title input when component mounts
    const timeout = setTimeout(() => {
      titleInputRef.current?.focus();
    }, 100);
    return () => clearTimeout(timeout);
  }, []);

  const communities = user?.communities || [];

  const isFormValid = title.trim().length > 0 && content.trim().length > 0 && !!selectedCommunity;

  const handleSubmit = () => {
    if (!isFormValid) {
      Alert.alert("Incomplete Post", "Please provide a title, content, and select a community.");
      return;
    }
    createThread(title, content, selectedCommunity, uploadedImages);
  };

  const handleCameraPress = async () => {
    try {
      const permissionResult = await ImagePicker.requestCameraPermissionsAsync();
      
      if (permissionResult.granted === false) {
        Alert.alert('Permission Required', 'Permission to access camera is required!');
        return;
      }

      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ['images'],
        allowsEditing: true,
        aspect: [16, 9],
        quality: 0.8,
        base64: false,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        const asset = result.assets[0];
        const mimeType = asset.mimeType || 'image/jpeg';
        await uploadImage(asset.uri, mimeType);
      }
    } catch (error) {
      console.error('Error taking photo:', error);
      Alert.alert('Error', 'Failed to take photo. Please try again.');
    }
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.Background.background0,
    },
    innerContainer: {
      flex: 1,
      padding: theme.spacing.spacing.s4,
    },
    topNavBar: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      paddingVertical: theme.spacing.spacing.s1,
      marginBottom: theme.spacing.spacing.s8,
    },
    titleInput: {
      fontFamily: theme.typography.fontFamily.merryweather,
      fontSize: theme.typography.headingSize.lg,
      color: theme.colors.Text.text900,
      paddingBottom: theme.spacing.spacing.s1,
    },
    contentInput: {
      fontFamily: theme.typography.fontFamily.inter,
      fontSize: theme.typography.textSize.sm,
      color: theme.colors.Text.text900,
      minHeight: 100,
      textAlignVertical: "top",
    },
    optionRow: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginVertical: theme.spacing.spacing.s2,
    },
    optionText: {
      fontFamily: theme.typography.fontFamily.interMedium,
      fontSize: theme.typography.textSize.md,
      color: theme.colors.Text.text900,
    },
    communitySelectionContainer: {
      flexDirection: "row",
      flexWrap: "wrap",
      alignItems: "center",
      justifyContent: "flex-end",
      flex: 1,
      marginLeft: theme.spacing.spacing.s8,
    },
    communityBadge: {
      paddingVertical: theme.spacing.spacing.s1,
      paddingHorizontal: theme.spacing.spacing.s2,
      borderRadius: theme.spacing.borderRadius.xl2,
      margin: theme.spacing.spacing.s0_5,
    },
    communityBadgeText: {
      fontFamily: theme.typography.fontFamily.inter,
      fontSize: theme.typography.textSize.sm,
    },
    errorText: {
        color: theme.colors.Secondary.secondary500,
        textAlign: 'center',
        marginTop: theme.spacing.spacing.s2,
    },
    bottomBar: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      padding: theme.spacing.spacing.s4,
      backgroundColor: theme.colors.Background.background0,
    },
    iconContainer: {
      flexDirection: "row",
      paddingVertical: theme.spacing.spacing.s2,
    },
    icon: {
      marginRight: theme.spacing.spacing.s4,
    },
    imageContainer: {
      marginVertical: theme.spacing.spacing.s3,
    },
    imagePreview: {
      width: 120,
      height: 80,
      borderRadius: theme.spacing.borderRadius.md,
      marginRight: theme.spacing.spacing.s2,
      marginBottom: theme.spacing.spacing.s2,
      position: 'relative',
    },
    imagePreviewContent: {
      width: '100%',
      height: '100%',
      borderRadius: theme.spacing.borderRadius.md,
    },
    removeImageButton: {
      position: 'absolute',
      top: -8,
      right: -8,
      backgroundColor: theme.colors.Secondary.secondary500,
      borderRadius: 12,
      width: 24,
      height: 24,
      justifyContent: 'center',
      alignItems: 'center',
    },
    imagesScrollContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
    },
  });



  return (
    <KeyboardAvoidingView style={styles.container} behavior="padding" keyboardVerticalOffset={70}>
      <SafeAreaView style={{flex: 1}}>
        <ScrollView contentContainerStyle={{ flex: 1 }} keyboardShouldPersistTaps="handled">
          <View style={styles.innerContainer}>
            <View style={styles.topNavBar}>
              <TouchableOpacity onPress={() => router.back()}>
                <XCircle size={24} color={theme.colors.Text.text950} />
              </TouchableOpacity>
            </View>
            <TextInput
              ref={titleInputRef}
              placeholder="Title"
              placeholderTextColor={theme.colors.Text.text500}
              style={styles.titleInput}
              value={title}
              onChangeText={setTitle}
            />
            <TextInput
              placeholder="Body text"
              placeholderTextColor={theme.colors.Text.text500}
              style={styles.contentInput}
              multiline
              value={content}
              onChangeText={setContent}
            />
            
            {/* Image Preview Section */}
            {uploadedImages.length > 0 && (
              <View style={styles.imageContainer}>
                <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                  <View style={styles.imagesScrollContainer}>
                    {uploadedImages.map((imageUrl, index) => (
                      <View key={index} style={styles.imagePreview}>
                        <RNImage
                          source={{ uri: imageUrl }}
                          style={styles.imagePreviewContent}
                          resizeMode="cover"
                        />
                        <TouchableOpacity
                          style={styles.removeImageButton}
                          onPress={() => removeImage(imageUrl)}
                        >
                          <X size={16} color={theme.colors.Background.background0} />
                        </TouchableOpacity>
                      </View>
                    ))}
                  </View>
                </ScrollView>
              </View>
            )}

            <View style={styles.iconContainer}>
              <TouchableOpacity 
                style={styles.icon} 
                onPress={handleCameraPress}
                disabled={isUploading}
              >
                {isUploading ? (
                  <ActivityIndicator size="small" color={theme.colors.Text.text500} />
                ) : (
                  <Camera size={24} color={theme.colors.Text.text500} />
                )}
              </TouchableOpacity>
              <TouchableOpacity 
                style={styles.icon} 
                onPress={selectAndUploadImage}
                disabled={isUploading}
              >
                <Image size={24} color={theme.colors.Text.text500} />
              </TouchableOpacity>
            </View>
          </View>
          <View style={styles.bottomBar}>
            <CommunitySelector
              communities={communities}
              selectedCommunity={selectedCommunity}
              onSelect={setSelectedCommunity}
              placeholder="Select a community"
            />
            <Button
              title="Post"
              onPress={handleSubmit}
              size="small"
              variant="iconNeutral"
              backgroundColor={theme.colors.Primary.primary500}
              icon={createLoading || isUploading ? <ActivityIndicator color={theme.colors.Background.background0}/> : <Plus size={20} color={theme.colors.Background.background0} />}
              iconPosition="right"
              disabled={!isFormValid || createLoading || isUploading}
            />
          </View>
        </ScrollView>
      </SafeAreaView>
    </KeyboardAvoidingView>
  );
};

export default CreatePostScreen;