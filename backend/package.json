{"name": "backend", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "engines": {"node": ">=22.0.0 "}, "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "node scripts/set-db-env.js nest start", "start:dev": "node scripts/set-db-env.js nest start --watch", "start:debug": "node scripts/set-db-env.js nest start --debug --watch", "start:prod": "NODE_ENV=production node scripts/set-db-env.js node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "db:seed:mongo": "node scripts/set-db-env.js ts-node src/mongo/seed/mongoSeed.ts", "db:seed:debug": "node scripts/set-db-env.js ts-node src/mongo/seed/mongoDebugSeed.ts", "db:seed:postgres": "npm run prisma:dev -- ts-node prisma/prismaSeed.ts", "db:seed:postgres:prod": "npm run prisma:prod -- ts-node prisma/prismaSeed.ts", "db:seed:prod": "node scripts/set-db-env.js ts-node src/mongo/seed/initProdSeed.ts", "prisma:dev": "node scripts/set-db-env.js npx prisma", "prisma:prod": "NODE_ENV=production node scripts/set-db-env.js npx prisma", "prisma:dev:migrate": "npm run prisma:dev -- migrate dev", "prisma:prod:deploy": "npm run prisma:prod -- migrate deploy", "prisma:dev:studio": "npm run prisma:dev -- studio", "prisma:prod:studio": "npm run prisma:prod -- studio", "test:onboarding": "jest src/tests/user-onboarding.spec.ts", "test:cleanup": "jest src/tests/cleanup-user.spec.ts"}, "dependencies": {"@apollo/server": "^4.12.2", "@google-cloud/storage": "^7.16.0", "@nestjs/apollo": "^13.1.0", "@nestjs/common": "^11.0.1", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.0.1", "@nestjs/graphql": "^13.1.0", "@nestjs/jwt": "^11.0.0", "@nestjs/mongoose": "^11.0.3", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.0.1", "@prisma/client": "^6.8.2", "@types/mongodb": "^4.0.6", "bcryptjs": "^3.0.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "dotenv": "^16.5.0", "firebase-admin": "^13.4.0", "graphql": "^16.11.0", "mongodb": "^6.17.0", "mongoose": "^8.16.1", "passport": "^0.7.0", "passport-custom": "^1.1.1", "passport-jwt": "^4.0.1", "prisma": "^6.8.2", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@types/bcryptjs": "^2.4.6", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/node": "^22.10.7", "@types/passport-jwt": "^4.0.1", "@types/supertest": "^6.0.2", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "globals": "^16.0.0", "jest": "^29.7.0", "prettier": "^3.4.2", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "prisma": {"seed": "ts-node prisma/prismaSeed.ts"}}