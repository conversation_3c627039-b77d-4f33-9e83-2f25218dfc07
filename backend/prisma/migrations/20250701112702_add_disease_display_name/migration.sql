/*
  Warnings:

  - The values [SUSPECTING] on the enum `UserRole` will be removed. If these variants are still used in the database, this will fail.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "health"."UserRole_new" AS ENUM ('DIAGNOSED', 'UNDIAGNOSED', 'CAREGIVER');
ALTER TABLE "health"."UserDiseaseProfile" ALTER COLUMN "userRole" TYPE "health"."UserRole_new" USING ("userRole"::text::"health"."UserRole_new");
ALTER TYPE "health"."UserRole" RENAME TO "UserRole_old";
ALTER TYPE "health"."UserRole_new" RENAME TO "UserRole";
DROP TYPE "health"."UserRole_old";
COMMIT;

-- AlterTable
ALTER TABLE "public"."Disease" ADD COLUMN     "displayName" TEXT;
