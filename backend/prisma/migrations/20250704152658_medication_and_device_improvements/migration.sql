-- AlterTable: Update MedicationMaster to new structure
-- First, add new columns to existing table
ALTER TABLE "public"."MedicationMaster" ADD COLUMN IF NOT EXISTS "genericName" TEXT;
ALTER TABLE "public"."MedicationMaster" ADD COLUMN IF NOT EXISTS "brandNames" TEXT[];
ALTER TABLE "public"."MedicationMaster" ADD COLUMN IF NOT EXISTS "localizedNames" JSONB;
ALTER TABLE "public"."MedicationMaster" ADD COLUMN IF NOT EXISTS "atcCode" TEXT;
ALTER TABLE "public"."MedicationMaster" ADD COLUMN IF NOT EXISTS "drugClass" TEXT;
ALTER TABLE "public"."MedicationMaster" ADD COLUMN IF NOT EXISTS "drugType" TEXT;

-- Migrate existing data: copy 'name' to 'genericName' if genericName is null
UPDATE "public"."MedicationMaster" SET "genericName" = "name" WHERE "genericName" IS NULL;

-- Make genericName NOT NULL and add unique constraint
ALTER TABLE "public"."MedicationMaster" ALTER COLUMN "genericName" SET NOT NULL;
ALTER TABLE "public"."MedicationMaster" ADD CONSTRAINT "MedicationMaster_genericName_key" UNIQUE ("genericName");

-- Create index on genericName
CREATE INDEX IF NOT EXISTS "MedicationMaster_genericName_idx" ON "public"."MedicationMaster"("genericName");

-- Rename table to match new schema
ALTER TABLE "public"."MedicationMaster" RENAME TO "medication_master";

-- Drop old name column and its constraints/indexes
ALTER TABLE "public"."medication_master" DROP COLUMN IF EXISTS "name";
ALTER TABLE "public"."medication_master" DROP COLUMN IF EXISTS "description";

-- CreateTable: Add new medical_device table
CREATE TABLE IF NOT EXISTS "public"."medical_device" (
    "id" TEXT NOT NULL,
    "genericName" TEXT NOT NULL,
    "brandNames" TEXT[],
    "localizedNames" JSONB,
    "deviceClass" TEXT,
    "description" TEXT,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "medical_device_pkey" PRIMARY KEY ("id")
);

-- CreateIndex for medical_device
CREATE UNIQUE INDEX IF NOT EXISTS "medical_device_genericName_key" ON "public"."medical_device"("genericName");
CREATE INDEX IF NOT EXISTS "medical_device_genericName_idx" ON "public"."medical_device"("genericName");
CREATE INDEX IF NOT EXISTS "medical_device_deviceClass_idx" ON "public"."medical_device"("deviceClass");

-- CreateTable: Add UserMedicalDevice table
CREATE TABLE IF NOT EXISTS "health"."UserMedicalDevice" (
    "user_healthID" TEXT NOT NULL,
    "medicalDeviceId" TEXT NOT NULL,
    "notes" TEXT,
    "startDate" TIMESTAMP(3),
    "endDate" TIMESTAMP(3),
    "isCurrent" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "UserMedicalDevice_pkey" PRIMARY KEY ("user_healthID","medicalDeviceId")
);

-- CreateIndex for UserMedicalDevice
CREATE INDEX IF NOT EXISTS "UserMedicalDevice_user_healthID_idx" ON "health"."UserMedicalDevice"("user_healthID");
CREATE INDEX IF NOT EXISTS "UserMedicalDevice_medicalDeviceId_idx" ON "health"."UserMedicalDevice"("medicalDeviceId");

-- AddForeignKey for UserMedicalDevice
ALTER TABLE "health"."UserMedicalDevice" ADD CONSTRAINT "UserMedicalDevice_medicalDeviceId_fkey" FOREIGN KEY ("medicalDeviceId") REFERENCES "public"."medical_device"("id") ON DELETE RESTRICT ON UPDATE CASCADE; 