import { PrismaClient } from '@prisma/client';
import { medications as medicationsData } from '../../ChroniCare/scr/data/medications';

const prisma = new PrismaClient();

async function main() {
  console.log(`Start seeding public schema...`);

  const ibd = await prisma.disease.create({
    data: {
      name: 'Inflammatory bowel disease',
      description:
        'A group of inflammatory conditions of the colon and small intestine.',
      isActive: true,
      subDiseases: {
        create: [
          {
            name: "<PERSON><PERSON>hn's Disease",
            description:
              'A type of inflammatory bowel disease (IBD) that may affect any part of the gastrointestinal tract from mouth to anus.',
            isActive: true,
            icdCode: 'DD70',
          },
          {
            name: 'Ulcerative Colitis',
            description:
              'A type of inflammatory bowel disease (IBD) that affects the lining of the large intestine (colon) and rectum.',
            isActive: true,
            icdCode: 'DD71',
          },
          {
            name: 'Inflammatory Bowel Disease',
            description:
              'A common disorder that affects the large intestine. Signs and symptoms include cramping, abdominal pain, bloating, gas, and diarrhea or constipation, or both.',
            isActive: true,
            icdCode: 'DD7Z',
          },
        ],
      },
    },
    include: {
      subDiseases: true,
    },
  });
  console.log(`Created disease 'Inflammatory bowel disease' with id: ${ibd.id}`);
  for (const subDisease of ibd.subDiseases) {
    console.log(
      `Created sub-disease '${subDisease.name}' with id: ${subDisease.id}`,
    );
  }

  const ibdCommunity = await prisma.community.create({
    data: {
      name: 'Inflammatory bowel disease',
      description: 'A community for people affected by IBD.',
      diseaseGroupId: ibd.id,
      isActive: true,
    },
  });
  console.log(
    `Created community 'Inflammatory bowel disease' with id: ${ibdCommunity.id}`,
  );

  await prisma.disease.update({
    where: { id: ibd.id },
    data: { primaryCommunityId: ibdCommunity.id },
  });
  console.log(`Updated 'Inflammatory bowel disease' with primary community.`);

  for (const med of medicationsData) {
    await prisma.medicationMaster.upsert({
      where: { id: med.id },
      update: {
        genericName: med.genericName,
        brandNames: med.brandNames,
        localizedNames: med.localizedNames || undefined,
        atcCode: med.atcCode,
        drugClass: med.drugClass,
        drugType: med.drugType,
      },
      create: {
        id: med.id,
        genericName: med.genericName,
        brandNames: med.brandNames,
        localizedNames: med.localizedNames || undefined,
        atcCode: med.atcCode,
        drugClass: med.drugClass,
        drugType: med.drugType,
      },
    });
  }
  console.log(`Seeded ${medicationsData.length} medications.`);

  const symptoms = [
    {
      name: 'Diarrhea',
      description: 'Loose, watery, and more-frequent bowel movements.',
    },
    {
      name: 'Abdominal pain and cramping',
      description: 'Pain or discomfort in the abdomen.',
    },
    { name: 'Rectal bleeding', description: 'Blood in the stool.' },
    {
      name: 'Weight loss',
      description: 'Unintentional decrease in body weight.',
    },
    {
      name: 'Fatigue',
      description: 'Persistent feeling of tiredness or weakness.',
    },
  ];

  await prisma.symptom.createMany({
    data: symptoms,
    skipDuplicates: true,
  });
  console.log(`Seeded ${symptoms.length} symptoms.`);

  const createdSymptoms = await prisma.symptom.findMany();
  const allDiseases = [ibd, ...ibd.subDiseases];
  const diseaseSymptomLinks: { diseaseId: string; symptomId: string }[] = [];

  for (const disease of allDiseases) {
    for (const symptom of createdSymptoms) {
      diseaseSymptomLinks.push({
        diseaseId: disease.id,
        symptomId: symptom.id,
      });
    }
  }

  await prisma.diseaseSymptom.createMany({
    data: diseaseSymptomLinks,
    skipDuplicates: true,
  });
  console.log(`Linked symptoms to diseases.`);

  console.log(`Seeding public schema finished.`);
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
